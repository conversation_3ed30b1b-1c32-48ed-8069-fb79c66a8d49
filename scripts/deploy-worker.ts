#!/usr/bin/env node

import { spawn } from 'child_process';
import { readFileSync } from 'fs';
import { join } from 'path';

const WORKER_DIR = join(process.cwd(), 'workers/image-gen');

async function runCommand(command: string, args: string[], cwd?: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, {
      cwd: cwd || process.cwd(),
      stdio: ['inherit', 'inherit', 'inherit'],
    });

    proc.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    proc.on('error', (error) => {
      reject(error);
    });
  });
}

async function setWorkerSecrets(): Promise<void> {
  console.log('🔐 Setting up Worker secrets...');
  
  const envFile = join(process.cwd(), '.env.local');
  const envContent = readFileSync(envFile, 'utf-8');
  
  const secrets = {
    OPENAI_API_KEY: envContent.match(/OPENAI_API_KEY=(.+)/)?.[1]?.trim(),
    SUPABASE_URL: envContent.match(/NEXT_PUBLIC_SUPABASE_URL=(.+)/)?.[1]?.trim(),
    SUPABASE_SERVICE_KEY: envContent.match(/SUPABASE_SERVICE_KEY=(.+)/)?.[1]?.trim(),
    WORKER_API_KEY: envContent.match(/WORKER_API_KEY=(.+)/)?.[1]?.trim(),
  };

  for (const [key, value] of Object.entries(secrets)) {
    if (value && value !== 'your-secure-worker-api-key') {
      console.log(`Setting secret: ${key}`);
      await runCommand('bun', ['wrangler', 'secret', 'put', key], WORKER_DIR);
      // Note: You'll need to manually enter the secret value when prompted
    }
  }
}

async function deployWorker(): Promise<void> {
  console.log('🚀 Starting Worker deployment...');
  
  try {
    // Change to worker directory
    process.chdir(WORKER_DIR);
    
    // Install dependencies
    console.log('📦 Installing Worker dependencies...');
    await runCommand('bun', ['install']);
    
    // Set up secrets
    await setWorkerSecrets();
    
    // Deploy the worker
    console.log('🚀 Deploying Worker...');
    await runCommand('bun', ['wrangler', 'deploy']);
    
    console.log('✅ Worker deployed successfully!');
    console.log('🔗 Don\'t forget to update WORKER_BASE_URL in your .env.local file');
    
  } catch (error) {
    console.error('❌ Deployment failed:', error);
    process.exit(1);
  }
}

// Run the deployment
deployWorker();