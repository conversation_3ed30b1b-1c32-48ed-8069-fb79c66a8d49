{"name": "crybaby-image-worker", "version": "1.0.0", "description": "Cloudflare Worker for CryBaby image generation", "main": "src/index.ts", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev"}, "keywords": ["cloudflare", "worker", "image", "generation"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@cloudflare/workers-types": "^4.20241205.0", "typescript": "^5.3.3", "wrangler": "^3.78.12"}, "dependencies": {"@silvia-odwyer/photon": "^0.3.3", "@supabase/supabase-js": "^2.39.0", "openai": "^4.26.0"}}