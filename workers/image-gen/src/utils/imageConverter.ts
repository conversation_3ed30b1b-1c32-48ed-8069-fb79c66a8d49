/**
 * Convert JPEG buffer to PNG buffer using Canvas API (fallback for WebAssembly issues)
 * @param jpegBuffer - Input JPEG image buffer
 * @returns Promise<ArrayBuffer> - Output PNG image buffer
 */
export async function convertJpegToPng(jpegBuffer: ArrayBuffer): Promise<ArrayBuffer> {
  console.log('🔄 Starting JPEG to PNG conversion...');
  const startTime = Date.now();
  
  try {
    // For now, we'll use a fallback approach until WebAssembly is properly configured
    // This will be enhanced with proper image conversion in a future update
    console.log('⚠️ WebAssembly conversion not available, using fallback approach');
    
    // Create a blob from the JPEG buffer
    const blob = new Blob([jpegBuffer], { type: 'image/jpeg' });
    
    // Create an ImageBitmap from the blob
    const imageBitmap = await createImageBitmap(blob);
    
    // Create an OffscreenCanvas
    const canvas = new OffscreenCanvas(imageBitmap.width, imageBitmap.height);
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get 2D context from OffscreenCanvas');
    }
    
    // Draw the image onto the canvas
    ctx.drawImage(imageBitmap, 0, 0);
    
    // Convert to PNG blob
    const pngBlob = await canvas.convertToBlob({ type: 'image/png' });
    
    // Convert blob to ArrayBuffer
    const pngBuffer = await pngBlob.arrayBuffer();
    
    const duration = Date.now() - startTime;
    console.log(`✅ JPEG to PNG conversion completed in ${duration}ms`);
    
    return pngBuffer;
  } catch (error) {
    const duration = Date.now() - startTime;
    console.error(`❌ JPEG to PNG conversion failed after ${duration}ms:`, error);
    throw new Error(`Failed to convert JPEG to PNG: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Detect image format from buffer
 * @param buffer - Image buffer
 * @returns 'jpeg' | 'png' | 'unknown'
 */
export function detectImageFormat(buffer: ArrayBuffer): 'jpeg' | 'png' | 'unknown' {
  const uint8Array = new Uint8Array(buffer);
  
  // Check for JPEG signature (FF D8)
  if (uint8Array[0] === 0xFF && uint8Array[1] === 0xD8) {
    return 'jpeg';
  }
  
  // Check for PNG signature (89 50 4E 47)
  if (uint8Array[0] === 0x89 && uint8Array[1] === 0x50 && 
      uint8Array[2] === 0x4E && uint8Array[3] === 0x47) {
    return 'png';
  }
  
  return 'unknown';
}

/**
 * Smart image processing: convert JPEG to PNG if needed
 * @param buffer - Input image buffer
 * @returns Promise<{buffer: ArrayBuffer, format: string, converted: boolean}>
 */
export async function processImageBuffer(buffer: ArrayBuffer): Promise<{
  buffer: ArrayBuffer;
  format: string;
  converted: boolean;
}> {
  console.log(`🔍 Processing image buffer: ${buffer?.byteLength || 0} bytes`);
  
  if (!buffer || buffer.byteLength === 0) {
    throw new Error("Invalid image buffer provided to processImageBuffer");
  }
  
  const format = detectImageFormat(buffer);
  
  console.log(`📸 Detected image format: ${format}`);
  
  if (format === 'jpeg') {
    console.log('🔄 Converting JPEG to PNG for OpenAI compatibility...');
    try {
      const pngBuffer = await convertJpegToPng(buffer);
      return {
        buffer: pngBuffer,
        format: 'png',
        converted: true
      };
    } catch (error) {
      console.error('❌ JPEG to PNG conversion failed, using original buffer:', error);
      // Fallback: return original buffer and let OpenAI handle it
      return {
        buffer: buffer,
        format: 'jpeg',
        converted: false
      };
    }
  } else if (format === 'png') {
    console.log('✅ Image is already in PNG format');
    return {
      buffer: buffer,
      format: 'png',
      converted: false
    };
  } else {
    console.log('⚠️ Unknown image format, trying as PNG...');
    return {
      buffer: buffer,
      format: 'png',
      converted: false
    };
  }
}