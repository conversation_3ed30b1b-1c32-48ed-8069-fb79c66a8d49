// Watermarking utility for Cloudflare Workers (without Sharp)
export interface WatermarkOptions {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
  opacity?: number;
  padding?: number;
  scale?: number;
}

// Create a simple SVG logo as base64
function createLogoSVG(): string {
  const logoText = 'CryBaby';
  const logoSize = 200;
  
  return `
    <svg width="${logoSize}" height="60" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#6366f1;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
        <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
          <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
        </filter>
      </defs>
      
      <!-- Background with rounded corners -->
      <rect x="5" y="5" width="${logoSize - 10}" height="50" rx="10" ry="10" 
            fill="rgba(0,0,0,0.7)" filter="url(#shadow)"/>
      
      <!-- Main text -->
      <text x="${logoSize / 2}" y="35" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="20" font-weight="bold" 
            fill="url(#grad1)">
        ${logoText}
      </text>
      
      <!-- Subtitle -->
      <text x="${logoSize / 2}" y="50" text-anchor="middle" 
            font-family="Arial, sans-serif" font-size="10" 
            fill="rgba(255,255,255,0.8)">
        AI Image Bot
      </text>
    </svg>
  `;
}

export function getWatermarkConfig(): WatermarkOptions {
  // Default configuration since we can't access process.env directly
  return {
    position: 'bottom-right',
    opacity: 0.8,
    padding: 20,
    scale: 0.12
  };
}

export function isWatermarkEnabled(): boolean {
  // Always enabled for now - could be made configurable via environment variable
  return true;
}

export async function watermarkImage(
  imageUrl: string,
  options: WatermarkOptions = {}
): Promise<ArrayBuffer> {
  try {
    // Check if watermarking is enabled
    if (!isWatermarkEnabled()) {
      // If disabled, just download and return the original image
      const response = await fetch(imageUrl);
      if (!response.ok) {
        throw new Error(`Failed to download image: ${response.statusText}`);
      }
      return await response.arrayBuffer();
    }

    // Download the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }
    
    const imageBuffer = await response.arrayBuffer();
    
    // For now, return the original image as-is since we can't easily do
    // image manipulation in Cloudflare Workers without additional libraries
    // TODO: Implement proper watermarking using Canvas API or WebAssembly
    console.warn('Watermarking not fully implemented in Worker environment, returning original image');
    
    return imageBuffer;
  } catch (error) {
    console.error('Error downloading and watermarking image:', error);
    throw error;
  }
}

// Alternative implementation using Canvas API (if available)
export async function watermarkImageWithCanvas(
  imageUrl: string,
  options: WatermarkOptions = {}
): Promise<ArrayBuffer> {
  try {
    const finalOptions = { ...getWatermarkConfig(), ...options };
    
    // Download the image
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`Failed to download image: ${response.statusText}`);
    }
    
    const imageBuffer = await response.arrayBuffer();
    
    // Create image from buffer
    const imageBlob = new Blob([imageBuffer]);
    const imageBitmap = await createImageBitmap(imageBlob);
    
    // Create canvas
    const canvas = new OffscreenCanvas(imageBitmap.width, imageBitmap.height);
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Could not get canvas context');
    }
    
    // Draw the original image
    ctx.drawImage(imageBitmap, 0, 0);
    
    // Create logo SVG
    const logoSVG = createLogoSVG();
    const logoBlob = new Blob([logoSVG], { type: 'image/svg+xml' });
    const logoImageBitmap = await createImageBitmap(logoBlob);
    
    // Calculate logo position
    const logoWidth = Math.floor(imageBitmap.width * finalOptions.scale!);
    const logoHeight = Math.floor(logoWidth * 0.3); // Maintain aspect ratio
    
    let x: number, y: number;
    
    switch (finalOptions.position) {
      case 'bottom-right':
        x = imageBitmap.width - logoWidth - finalOptions.padding!;
        y = imageBitmap.height - logoHeight - finalOptions.padding!;
        break;
      case 'bottom-left':
        x = finalOptions.padding!;
        y = imageBitmap.height - logoHeight - finalOptions.padding!;
        break;
      case 'top-right':
        x = imageBitmap.width - logoWidth - finalOptions.padding!;
        y = finalOptions.padding!;
        break;
      case 'top-left':
        x = finalOptions.padding!;
        y = finalOptions.padding!;
        break;
      case 'center':
        x = (imageBitmap.width - logoWidth) / 2;
        y = (imageBitmap.height - logoHeight) / 2;
        break;
      default:
        x = imageBitmap.width - logoWidth - finalOptions.padding!;
        y = imageBitmap.height - logoHeight - finalOptions.padding!;
    }
    
    // Set opacity and draw watermark
    ctx.globalAlpha = finalOptions.opacity!;
    ctx.drawImage(logoImageBitmap, x, y, logoWidth, logoHeight);
    
    // Convert canvas to blob
    const resultBlob = await canvas.convertToBlob({ type: 'image/png' });
    
    return await resultBlob.arrayBuffer();
  } catch (error) {
    console.error('Error watermarking image with canvas:', error);
    throw error;
  }
}