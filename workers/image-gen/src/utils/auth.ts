import { Env } from '../env';

export function verifyApi<PERSON><PERSON>(request: Request, env: Env): boolean {
  const authHeader = request.headers.get('Authorization');
  const apiKeyHeader = request.headers.get('X-API-Key');
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    return token === env.WORKER_API_KEY;
  }
  
  if (apiKeyHeader) {
    return apiKeyHeader === env.WORKER_API_KEY;
  }
  
  return false;
}

export function createUnauthorizedResponse(): Response {
  return new Response(JSON.stringify({ error: 'Unauthorized' }), {
    status: 401,
    headers: { 'Content-Type': 'application/json' },
  });
}