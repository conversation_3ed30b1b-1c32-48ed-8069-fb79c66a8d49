import { Env } from './env';
import { handleCors, addCorsHeaders } from './utils/cors';
import { verifyApi<PERSON>ey, createUnauthorizedResponse } from './utils/auth';
import { generateImage, transformImage } from './services/openai';
import { logGeneration } from './services/supabase';

export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // Handle CORS preflight requests
    const corsResponse = handleCors(request);
    if (corsResponse) {
      return corsResponse;
    }

    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;

    console.log(`📨 ${method} ${path}`);

    try {
      // Health check endpoint (no auth required)
      if (path === '/health' && method === 'GET') {
        return addCorsHeaders(new Response(JSON.stringify({
          status: 'ok',
          message: 'CryBaby Image Worker is running',
          timestamp: new Date().toISOString()
        }), {
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      // Verify API key for all other endpoints
      if (!verifyApiKey(request, env)) {
        return addCorsHeaders(createUnauthorizedResponse());
      }

      // Generate image endpoint
      if (path === '/generate' && method === 'POST') {
        return addCorsHeaders(await handleGenerateImage(request, env));
      }

      // Transform image endpoint
      if (path === '/transform' && method === 'POST') {
        return addCorsHeaders(await handleTransformImage(request, env));
      }

      // 404 for unknown endpoints
      return addCorsHeaders(new Response(JSON.stringify({
        error: 'Not Found',
        message: 'Endpoint not found'
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      }));

    } catch (error) {
      console.error('❌ Worker error:', error);
      
      return addCorsHeaders(new Response(JSON.stringify({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred'
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }));
    }
  }
};

async function handleGenerateImage(request: Request, env: Env): Promise<Response> {
  try {
    const body = await request.json() as {
      prompt: string;
      userId: number;
    };

    if (!body.prompt || typeof body.prompt !== 'string') {
      return new Response(JSON.stringify({
        error: 'Bad Request',
        message: 'prompt is required and must be a string'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!body.userId || typeof body.userId !== 'number') {
      return new Response(JSON.stringify({
        error: 'Bad Request',
        message: 'userId is required and must be a number'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log(`🎨 Generating image for user ${body.userId}`);

    const result = await generateImage(body.prompt, env);

    // Log the generation attempt
    await logGeneration(
      body.userId,
      body.prompt,
      result.success,
      env,
      result.imageUrl,
      result.error
    );

    return new Response(JSON.stringify(result), {
      status: result.success ? 200 : 500,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Error in handleGenerateImage:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process image generation request'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

async function handleTransformImage(request: Request, env: Env): Promise<Response> {
  try {
    const body = await request.json() as {
      imageUrl: string;
      userId: number;
    };

    if (!body.imageUrl || typeof body.imageUrl !== 'string') {
      return new Response(JSON.stringify({
        error: 'Bad Request',
        message: 'imageUrl is required and must be a string'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (!body.userId || typeof body.userId !== 'number') {
      return new Response(JSON.stringify({
        error: 'Bad Request',
        message: 'userId is required and must be a number'
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    console.log(`🖼️ Transforming image for user ${body.userId}`);

    const result = await transformImage(body.imageUrl, env);

    // Log the transformation attempt
    await logGeneration(
      body.userId,
      'Direct image transformation',
      result.success,
      env,
      result.imageUrl,
      result.error
    );

    return new Response(JSON.stringify(result), {
      status: result.success ? 200 : 500,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error('❌ Error in handleTransformImage:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: 'Failed to process image transformation request'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}