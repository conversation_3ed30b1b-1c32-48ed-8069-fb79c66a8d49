import OpenAI from 'openai';
import { Env } from '../env';
import { watermarkImage } from '../utils/watermark';
import { uploadImageToStorage } from './supabase';
import { processImageBuffer } from '../utils/imageConverter';

export interface ImageGenerationResult {
  success: boolean;
  imageUrl?: string;
  originalUrl?: string;
  error?: string;
}

// Fetch with retry logic and timeout handling
async function fetchWithRetry(url: string, options: RequestInit & { timeout?: number } = {}, maxRetries = 3): Promise<Response> {
  const { timeout = 30000, ...fetchOptions } = options;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        return response;
      }
      
      // If not ok but not the last retry, continue to next attempt
      if (attempt < maxRetries) {
        console.warn(`Fetch attempt ${attempt} failed with status ${response.status}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
        continue;
      }
      
      return response; // Return failed response on last attempt
    } catch (error) {
      console.error(`Fetch attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  
  throw new Error(`Failed to fetch after ${maxRetries} attempts`);
}

export async function generateImage(prompt: string, env: Env): Promise<ImageGenerationResult> {
  console.log(`🎨 Starting DALL-E 3 image generation with prompt: "${prompt.substring(0, 100)}..."`);
  const startTime = Date.now();

  try {
    const openai = new OpenAI({
      apiKey: env.OPENAI_API_KEY,
    });

    const response = await openai.images.generate({
      model: "dall-e-3",
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "hd",
    }, {
      timeout: 240000, // 4 minute timeout
    });

    const duration = Date.now() - startTime;
    console.log(`✅ DALL-E 3 generation completed in ${duration}ms`);

    const originalImageUrl = response.data?.[0]?.url;

    if (!originalImageUrl) {
      console.log('❌ No image URL found in response');
      return {
        success: false,
        error: "No image URL returned from OpenAI"
      };
    }

    // Add watermark to the image
    try {
      const watermarkedBuffer = await watermarkImage(originalImageUrl);

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `watermarked-${timestamp}.png`;

      // Upload watermarked image to Supabase Storage
      const watermarkedUrl = await uploadImageToStorage(watermarkedBuffer, filename, env);

      if (watermarkedUrl) {
        const totalDuration = Date.now() - startTime;
        console.log(`🎉 Image generation completed successfully in ${totalDuration}ms`);

        return {
          success: true,
          imageUrl: watermarkedUrl,
          originalUrl: originalImageUrl
        };
      } else {
        // If upload fails, return original image
        console.warn('Failed to upload watermarked image, returning original');
        return {
          success: true,
          imageUrl: originalImageUrl,
          originalUrl: originalImageUrl
        };
      }
    } catch (watermarkError) {
      console.error('Watermarking failed:', watermarkError);
      // If watermarking fails, return original image
      const totalDuration = Date.now() - startTime;
      console.log(`⚠️ Image generation completed with watermark failure in ${totalDuration}ms`);

      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    }
  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(`❌ Image generation failed after ${totalDuration}ms:`, error);
    
    let errorMessage = "Failed to generate image";
    
    if (error instanceof OpenAI.APIError) {
      errorMessage = `OpenAI API Error: ${error.message}`;
    } else if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (error.message.includes('ETIMEDOUT') || error.message.includes('connect')) {
        errorMessage = "Network connection failed. Please check your internet and try again.";
      } else if (error.message.includes('fetch failed')) {
        errorMessage = "Network error occurred. Please try again later.";
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

export async function transformImage(imageUrl: string, env: Env): Promise<ImageGenerationResult> {
  console.log(`🖼️ Starting DALL-E 3 image transformation process for URL: ${imageUrl.substring(0, 100)}...`);
  const processStartTime = Date.now();

  try {
    const openai = new OpenAI({
      apiKey: env.OPENAI_API_KEY,
    });

    // Download the image from Telegram with timeout and retry logic
    console.log(`📥 Downloading image from Telegram: ${imageUrl}`);
    const downloadStartTime = Date.now();

    const imageResponse = await fetchWithRetry(imageUrl, {
      method: 'GET',
      timeout: 30000,
    });

    const downloadDuration = Date.now() - downloadStartTime;
    console.log(`📥 Image download completed in ${downloadDuration}ms`);
    
    if (!imageResponse.ok) {
      console.error(`❌ Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
      throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
    }
    
    const imageBuffer = await imageResponse.arrayBuffer();
    console.log(`✅ Image downloaded successfully (${imageBuffer?.byteLength || 0} bytes)`);
    
    // Debug: Check if imageBuffer is valid
    if (!imageBuffer || imageBuffer.byteLength === 0) {
      throw new Error("Downloaded image buffer is empty or invalid");
    }

    // Check if image is under 25MB (GPT-Image-1 limit)
    if (imageBuffer.byteLength > 25 * 1024 * 1024) {
      throw new Error("Image is too large (over 25MB). Please use a smaller image.");
    }
    
    // Process the image buffer (convert JPEG to PNG if needed)
    const { buffer: processedBuffer, format, converted } = await processImageBuffer(imageBuffer);
    
    if (converted) {
      console.log(`🔄 Successfully converted JPEG to PNG (${processedBuffer.byteLength} bytes)`);
    }
    
    // DALL-E 3 createVariation only accepts PNG, so if we still have JPEG, we need to error
    if (format === 'jpeg') {
      throw new Error("DALL-E 3 image variation requires PNG format. JPEG conversion failed. Please try with a PNG image or a smaller JPEG image.");
    }
    
    const fileName = "input.png";
    const mimeType = "image/png";
    
    console.log(`📄 Creating File object: ${fileName}, size: ${processedBuffer.byteLength}, type: ${mimeType}`);
    const imageFile = new File([processedBuffer], fileName, { type: mimeType });

    // Use DALL-E 3's image variation feature to transform the image
    console.log(`🔄 Starting DALL-E 3 image variation transformation...`);
    console.log(`📊 Image file size: ${processedBuffer.byteLength} bytes`);
    const startTime = Date.now();

    let response: any;
    try {
      response = await openai.images.createVariation({
        image: imageFile,
        n: 1,
        size: "1024x1024",
      }, {
        timeout: 240000, // 4 minute timeout
      });

      const duration = Date.now() - startTime;
      console.log(`✅ DALL-E 3 image variation completed in ${duration}ms`);

      if (!response.data || response.data.length === 0) {
        throw new Error("OpenAI returned empty response");
      }

      console.log(`📊 DALL-E 3 returned ${response.data.length} image(s)`);
    } catch (openaiError) {
      const duration = Date.now() - startTime;
      console.error(`❌ DALL-E 3 API call failed after ${duration}ms:`, openaiError);
      const errorMessage = openaiError instanceof Error ? openaiError.message : String(openaiError);
      throw new Error(`DALL-E 3 image variation failed: ${errorMessage}`);
    }

    const originalImageUrl = response.data?.[0]?.url;

    if (!originalImageUrl) {
      return {
        success: false,
        error: "No image URL returned from OpenAI"
      };
    }

    // Add watermark to the image
    try {
      const watermarkedBuffer = await watermarkImage(originalImageUrl);

      // Generate unique filename
      const timestamp = Date.now();
      const filename = `transformed-${timestamp}.png`;

      // Upload watermarked image to Supabase Storage
      const watermarkedUrl = await uploadImageToStorage(watermarkedBuffer, filename, env);

      if (watermarkedUrl) {
        const totalDuration = Date.now() - processStartTime;
        console.log(`🎉 Image transformation completed successfully in ${totalDuration}ms`);

        return {
          success: true,
          imageUrl: watermarkedUrl,
          originalUrl: originalImageUrl
        };
      } else {
        // If upload fails, return original image
        console.warn('Failed to upload watermarked image, returning original');
        return {
          success: true,
          imageUrl: originalImageUrl,
          originalUrl: originalImageUrl
        };
      }
    } catch (watermarkError) {
      console.error('Watermarking failed:', watermarkError);
      // If watermarking fails, return original image
      const totalDuration = Date.now() - processStartTime;
      console.log(`⚠️ Image transformation completed with watermark failure in ${totalDuration}ms`);

      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    }
  } catch (error) {
    const totalDuration = Date.now() - processStartTime;
    console.error(`❌ Image transformation failed after ${totalDuration}ms:`, error);
    
    let errorMessage = "Failed to transform image";
    
    if (error instanceof OpenAI.APIError) {
      errorMessage = `OpenAI API Error: ${error.message}`;
    } else if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (error.message.includes('ETIMEDOUT') || error.message.includes('connect')) {
        errorMessage = "Network connection failed. Please check your internet and try again.";
      } else if (error.message.includes('fetch failed')) {
        errorMessage = "Network error occurred. Please try again later.";
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

// Import the image converter at the top of the file