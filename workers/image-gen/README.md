# CryBaby Image Generation Worker

A Cloudflare Worker that handles AI image generation and transformation for the CryBaby Telegram bot.

## Features

- 🎨 Text-to-image generation using OpenAI DALL-E 3
- 🖼️ Image transformation using OpenAI's variation API
- 🔐 Secure API authentication
- 🌊 Watermarking with CryBaby logo
- 📊 Integration with Supabase for rate limiting and logging
- ⚡ Edge computing for improved performance

## Architecture

This Worker offloads heavy image processing from the main Next.js application, providing:

- **Better Performance**: Edge compute closer to users
- **Scalability**: Automatic scaling without cold starts
- **Cost Efficiency**: Pay-per-request model
- **Reliability**: Cloudflare's global network
- **Isolation**: Separate processing environment

## API Endpoints

### POST `/generate`
Generate an image from text prompt.

**Request:**
```json
{
  "prompt": "A cat wearing sunglasses",
  "userId": 12345
}
```

**Response:**
```json
{
  "success": true,
  "imageUrl": "https://example.com/generated-image.png",
  "originalUrl": "https://openai-generated-url.com/image.png"
}
```

### POST `/transform`
Transform an existing image to CryBaby style.

**Request:**
```json
{
  "imageUrl": "https://example.com/input-image.png",
  "userId": 12345
}
```

**Response:**
```json
{
  "success": true,
  "imageUrl": "https://example.com/transformed-image.png",
  "originalUrl": "https://openai-generated-url.com/image.png"
}
```

### GET `/health`
Health check endpoint (no authentication required).

**Response:**
```json
{
  "status": "ok",
  "message": "CryBaby Image Worker is running",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## Environment Variables

Configure these secrets using `wrangler secret put`:

```bash
wrangler secret put OPENAI_API_KEY
wrangler secret put SUPABASE_URL
wrangler secret put SUPABASE_SERVICE_KEY
wrangler secret put WORKER_API_KEY
```

## Development

1. **Install dependencies:**
   ```bash
   bun install
   ```

2. **Run locally:**
   ```bash
   bun run dev
   ```

3. **Deploy:**
   ```bash
   bun run deploy
   ```

## Deployment from Main Project

From the main project root:

1. **Deploy Worker:**
   ```bash
   bun run deploy-worker
   ```

2. **Test Worker:**
   ```bash
   bun run test-worker
   ```

3. **Update environment variables:**
   Update `WORKER_BASE_URL` in `.env.local` with your deployed Worker URL.

## Security

- ✅ API key authentication required for all endpoints except `/health`
- ✅ CORS headers properly configured
- ✅ Input validation and sanitization
- ✅ Secure environment variable handling
- ✅ Rate limiting handled by main application via Supabase

## Monitoring

- All operations are logged to console
- Generation attempts are logged to Supabase
- Health check endpoint for monitoring
- Error tracking with detailed error messages

## Limitations

- Watermarking is simplified compared to Sharp-based solution
- Canvas API implementation may need refinement
- 4MB image size limit (OpenAI constraint)
- Worker execution time limits apply

## Future Improvements

- [ ] Enhanced watermarking using WebAssembly
- [ ] Image optimization and compression
- [ ] Caching layer for repeated requests
- [ ] Advanced error handling and retries
- [ ] Metrics and analytics integration