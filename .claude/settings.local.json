{"permissions": {"allow": ["Bash(bun add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(npx tsx:*)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(ls:*)", "Bash(git commit:*)", "Bash(gh repo create:*)", "Bash(bun run:*)", "Bash(bunx turbo:*)", "Bash(git push:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(grep:*)", "Bash(find:*)", "Bash(npm audit:*)", "Bash(gh pr:*)", "Bash(git fetch:*)", "Bash(git checkout:*)", "Bash(git pull:*)", "Bash(bun create:*)", "Bash(bun install:*)", "Bash(bun build:*)", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:oaidalleapiprodscus.blob.core.windows.net)", "Bash(bun wrangler deploy:*)", "Bash(bun wrangler tail:*)", "Bash(timeout 30 bun wrangler tail --format=json)", "WebFetch(domain:platform.openai.com)", "WebFetch(domain:apidog.com)", "WebFetch(domain:www.analyticsvidhya.com)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(env)", "<PERSON><PERSON>(bunx wrangler auth:*)", "<PERSON><PERSON>(bunx wrangler:*)", "Ba<PERSON>(curl -X POST https://crybaby-image-worker.oddofrancesco000.workers.dev/health )", "Bash(-H \"Authorization: Bearer PZ1NPEB5Y-HCSoYIWlvPcPcEtNTwLWgNYLlcd3xz\")", "Bash(npm search:*)", "Bash(npm install:*)", "<PERSON><PERSON>(wrangler deploy:*)", "Bash(wrangler tail:*)", "Bash(npx wrangler tail:*)"], "deny": []}}