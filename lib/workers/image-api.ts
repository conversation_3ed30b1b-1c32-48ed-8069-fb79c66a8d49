export interface ImageGenerationResult {
  success: boolean;
  imageUrl?: string;
  originalUrl?: string;
  error?: string;
}

export interface WorkerApiConfig {
  baseUrl: string;
  apiKey: string;
  timeout?: number;
  maxRetries?: number;
}

export class ImageWorkerClient {
  private config: WorkerApiConfig;

  constructor(config: WorkerApiConfig) {
    this.config = {
      timeout: 300000, // 5 minutes default
      maxRetries: 3,
      ...config
    };
  }

  private async makeRequest(
    endpoint: string,
    data: unknown,
    attempt: number = 1
  ): Promise<Response> {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.config.apiKey}`,
          'X-API-Key': this.config.apiKey,
        },
        body: JSON.stringify(data),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        return response;
      }

      // If not successful but not the last retry, throw to trigger retry
      if (attempt < this.config.maxRetries!) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response; // Return failed response on last attempt
    } catch (error) {
      console.error(`Worker API request attempt ${attempt} failed:`, error);
      
      if (attempt >= this.config.maxRetries!) {
        throw error;
      }

      // Wait before retry with exponential backoff
      const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      return this.makeRequest(endpoint, data, attempt + 1);
    }
  }

  async generateImage(prompt: string, userId: number): Promise<ImageGenerationResult> {
    const startTime = Date.now();
    console.log(`🎨 Worker API: Starting image generation for user ${userId}`);

    try {
      const response = await this.makeRequest('/generate', {
        prompt,
        userId
      });

      const result = await response.json() as ImageGenerationResult;
      
      const duration = Date.now() - startTime;
      console.log(`✅ Worker API: Image generation completed in ${duration}ms`);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Worker API: Image generation failed after ${duration}ms:`, error);

      let errorMessage = 'Failed to generate image';
      
      if (error instanceof Error) {
        if (error.name === 'AbortError' || error.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (error.message.includes('fetch failed')) {
          errorMessage = 'Network error occurred. Please try again later.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async transformImage(imageUrl: string, userId: number): Promise<ImageGenerationResult> {
    const startTime = Date.now();
    console.log(`🖼️ Worker API: Starting image transformation for user ${userId}`);

    try {
      const response = await this.makeRequest('/transform', {
        imageUrl,
        userId
      });

      const result = await response.json() as ImageGenerationResult;
      
      const duration = Date.now() - startTime;
      console.log(`✅ Worker API: Image transformation completed in ${duration}ms`);

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ Worker API: Image transformation failed after ${duration}ms:`, error);

      let errorMessage = 'Failed to transform image';
      
      if (error instanceof Error) {
        if (error.name === 'AbortError' || error.message.includes('timeout')) {
          errorMessage = 'Request timed out. Please try again.';
        } else if (error.message.includes('fetch failed')) {
          errorMessage = 'Network error occurred. Please try again later.';
        } else {
          errorMessage = error.message;
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.config.baseUrl}/health`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`,
          'X-API-Key': this.config.apiKey,
        }
      });

      return response.ok;
    } catch (error) {
      console.error('Worker API health check failed:', error);
      return false;
    }
  }
}

// Create a singleton instance
export const imageWorkerClient = new ImageWorkerClient({
  baseUrl: process.env.WORKER_BASE_URL || 'https://crybaby-image-worker.your-subdomain.workers.dev',
  apiKey: process.env.WORKER_API_KEY || 'your-worker-api-key'
});

// Export convenience functions
export async function generateImage(prompt: string, userId: number): Promise<ImageGenerationResult> {
  return imageWorkerClient.generateImage(prompt, userId);
}

export async function transformImage(imageUrl: string, userId: number): Promise<ImageGenerationResult> {
  return imageWorkerClient.transformImage(imageUrl, userId);
}