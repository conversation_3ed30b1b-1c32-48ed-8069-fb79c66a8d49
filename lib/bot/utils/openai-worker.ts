import { generateImage as workerGenerateImage, transformImage as workerTransformImage } from '@/lib/workers/image-api';

export interface ImageGenerationResult {
  success: boolean;
  imageUrl?: string;
  originalUrl?: string;
  error?: string;
}

export async function generateImage(prompt: string, userId: number): Promise<ImageGenerationResult> {
  console.log(`🎨 Using Worker API for image generation - User: ${userId}, Prompt: "${prompt.substring(0, 100)}..."`);
  
  try {
    const result = await workerGenerateImage(prompt, userId);
    
    if (result.success) {
      console.log(`✅ Worker API generation successful for user ${userId}`);
    } else {
      console.error(`❌ Worker API generation failed for user ${userId}: ${result.error}`);
    }
    
    return result;
  } catch (error) {
    console.error(`❌ Worker API call failed for user ${userId}:`, error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

export async function transformImage(imageUrl: string, userId: number): Promise<ImageGenerationResult> {
  console.log(`🖼️ Using Worker API for image transformation - User: ${userId}, URL: ${imageUrl.substring(0, 100)}...`);
  
  try {
    const result = await workerTransformImage(imageUrl, userId);
    
    if (result.success) {
      console.log(`✅ Worker API transformation successful for user ${userId}`);
    } else {
      console.error(`❌ Worker API transformation failed for user ${userId}: ${result.error}`);
    }
    
    return result;
  } catch (error) {
    console.error(`❌ Worker API call failed for user ${userId}:`, error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}