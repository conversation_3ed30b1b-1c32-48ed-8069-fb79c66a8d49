import OpenAI from "openai";
import { addWatermarkFromUrl } from "./watermark";

export const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY!,
});

// Fetch with retry logic and timeout handling
async function fetchWithRetry(url: string, options: RequestInit & { timeout?: number } = {}, maxRetries = 3): Promise<Response> {
  const { timeout = 30000, ...fetchOptions } = options;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      const response = await fetch(url, {
        ...fetchOptions,
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (response.ok) {
        return response;
      }
      
      // If not ok but not the last retry, continue to next attempt
      if (attempt < maxRetries) {
        console.warn(`Fetch attempt ${attempt} failed with status ${response.status}, retrying...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt)); // Exponential backoff
        continue;
      }
      
      return response; // Return failed response on last attempt
    } catch (error) {
      console.error(`Fetch attempt ${attempt} failed:`, error);
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
  
  throw new Error(`Failed to fetch after ${maxRetries} attempts`);
}

export interface ImageGenerationResult {
  success: boolean;
  imageUrl?: string;
  originalUrl?: string;
  error?: string;
}

export async function transformImage(imageUrl: string): Promise<ImageGenerationResult> {
  console.log(`🖼️ Starting image transformation process for URL: ${imageUrl.substring(0, 100)}...`);
  const processStartTime = Date.now();

  try {

    // Download the image from Telegram with timeout and retry logic
    console.log(`📥 Downloading image from Telegram: ${imageUrl}`);
    const downloadStartTime = Date.now();

    const imageResponse = await fetchWithRetry(imageUrl, {
      method: 'GET',
      timeout: 30000, // 30 second timeout
    });

    const downloadDuration = Date.now() - downloadStartTime;
    console.log(`📥 Image download completed in ${downloadDuration}ms`);
    if (!imageResponse.ok) {
      console.error(`❌ Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
      throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`);
    }
    
    const imageBuffer = await imageResponse.arrayBuffer();
    console.log(`✅ Image downloaded successfully (${imageBuffer.byteLength} bytes)`);

    // Convert image to PNG format using Sharp
    console.log(`🔄 Processing image with Sharp...`);
    const sharpStartTime = Date.now();

    let pngBuffer: Buffer;
    try {
      const sharp = (await import('sharp')).default;
      console.log(`📦 Sharp module loaded successfully`);

      pngBuffer = await sharp(Buffer.from(imageBuffer))
        .png()
        .resize(1024, 1024, { fit: 'inside', withoutEnlargement: true })
        .toBuffer();

      const sharpDuration = Date.now() - sharpStartTime;
      console.log(`🔄 Sharp processing completed in ${sharpDuration}ms`);

      console.log(`✅ Image processed to PNG (${pngBuffer.length} bytes)`);

      // Check if image is under 4MB (OpenAI limit)
      if (pngBuffer.length > 4 * 1024 * 1024) {
        throw new Error("Image is too large (over 4MB). Please use a smaller image.");
      }
    } catch (sharpError) {
      console.error(`❌ Sharp processing failed:`, sharpError);
      const errorMessage = sharpError instanceof Error ? sharpError.message : String(sharpError);
      throw new Error(`Image processing failed: ${errorMessage}`);
    }
    
    const imageFile = new File([pngBuffer], "input.png", { type: "image/png" });

    // Use OpenAI's image variation feature to transform the image
    console.log(`🔄 Starting OpenAI image variation transformation...`);
    console.log(`📊 Image file size: ${pngBuffer.length} bytes`);
    console.log(`📊 Using 4-minute timeout for OpenAI API`);
    const startTime = Date.now();

    let response: OpenAI.Images.ImagesResponse;
    try {
      response = await openai.images.createVariation({
        image: imageFile,
        n: 1,
        size: "1024x1024",
      }, {
        timeout: 240000, // 4 minute timeout for OpenAI API (increased from 60s)
      });

      const duration = Date.now() - startTime;
      console.log(`✅ Image variation completed in ${duration}ms`);

      if (!response.data || response.data.length === 0) {
        throw new Error("OpenAI returned empty response");
      }

      console.log(`📊 OpenAI returned ${response.data.length} image(s)`);
    } catch (openaiError) {
      const duration = Date.now() - startTime;
      console.error(`❌ OpenAI API call failed after ${duration}ms:`, openaiError);
      const errorMessage = openaiError instanceof Error ? openaiError.message : String(openaiError);
      throw new Error(`OpenAI image variation failed: ${errorMessage}`);
    }

    const originalImageUrl = response.data?.[0]?.url;

    if (!originalImageUrl) {
      return {
        success: false,
        error: "No image URL returned from OpenAI"
      };
    }

    // Add watermark to the image
    try {
      await addWatermarkFromUrl(originalImageUrl);

      // For now, skip Supabase storage upload and return watermarked image as base64
      // TODO: Configure Supabase storage bucket properly
      console.warn('Supabase storage not configured, returning original image');

      const totalDuration = Date.now() - processStartTime;
      console.log(`🎉 Image transformation completed successfully in ${totalDuration}ms`);

      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    } catch (watermarkError) {
      console.error('Watermarking failed:', watermarkError);
      // If watermarking fails, return original image
      const totalDuration = Date.now() - processStartTime;
      console.log(`⚠️ Image transformation completed with watermark failure in ${totalDuration}ms`);

      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    }
  } catch (error) {
    const totalDuration = Date.now() - processStartTime;
    console.error(`❌ Image transformation failed after ${totalDuration}ms:`, error);
    
    let errorMessage = "Failed to transform image";
    
    if (error instanceof OpenAI.APIError) {
      errorMessage = `OpenAI API Error: ${error.message}`;
    } else if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (error.message.includes('ETIMEDOUT') || error.message.includes('connect')) {
        errorMessage = "Network connection failed. Please check your internet and try again.";
      } else if (error.message.includes('fetch failed')) {
        errorMessage = "Network error occurred. Please try again later.";
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

export async function generateImage(prompt: string): Promise<ImageGenerationResult> {
  console.log(`🎨 Starting DALL-E 3 image generation with prompt: "${prompt.substring(0, 100)}..."`);
  const startTime = Date.now();

  try {

    const response = await openai.images.generate({
      model: "dall-e-3", // DALL-E 3 model for high-quality image generation
      prompt: prompt,
      n: 1,
      size: "1024x1024",
      quality: "hd", // Use "hd" for high quality with DALL-E 3
    }, {
      timeout: 240000, // 4 minute timeout for OpenAI API (increased from 60s)
    });

    const duration = Date.now() - startTime;
    console.log(`✅ DALL-E 3 generation completed in ${duration}ms`);

    const originalImageUrl = response.data?.[0]?.url;

    if (!originalImageUrl) {
      return {
        success: false,
        error: "No image URL returned from OpenAI"
      };
    }

    // Add watermark to the image
    try {
      await addWatermarkFromUrl(originalImageUrl);

      // For now, skip Supabase storage upload and return watermarked image as base64
      // TODO: Configure Supabase storage bucket properly
      console.warn('Supabase storage not configured, returning original image');

      const totalDuration = Date.now() - startTime;
      console.log(`🎉 Image generation completed successfully in ${totalDuration}ms`);

      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    } catch (watermarkError) {
      console.error('Watermarking failed:', watermarkError);
      // If watermarking fails, return original image
      const totalDuration = Date.now() - startTime;
      console.log(`⚠️ Image generation completed with watermark failure in ${totalDuration}ms`);

      return {
        success: true,
        imageUrl: originalImageUrl,
        originalUrl: originalImageUrl
      };
    }
  } catch (error) {
    const totalDuration = Date.now() - startTime;
    console.error(`❌ Image generation failed after ${totalDuration}ms:`, error);
    
    let errorMessage = "Failed to generate image";
    
    if (error instanceof OpenAI.APIError) {
      errorMessage = `OpenAI API Error: ${error.message}`;
    } else if (error instanceof Error) {
      if (error.name === 'AbortError' || error.message.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (error.message.includes('ETIMEDOUT') || error.message.includes('connect')) {
        errorMessage = "Network connection failed. Please check your internet and try again.";
      } else if (error.message.includes('fetch failed')) {
        errorMessage = "Network error occurred. Please try again later.";
      } else {
        errorMessage = error.message;
      }
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}