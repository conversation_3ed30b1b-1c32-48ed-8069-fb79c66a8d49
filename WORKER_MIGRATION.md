# CryBaby Image Generation Worker Migration

This document outlines the migration from direct OpenAI API calls to a Cloudflare Worker for image generation.

## Migration Overview

### Before: Direct OpenAI Integration
- Image generation happened directly in Next.js API routes
- Long-running processes (2-3 minutes) with `maxDuration = 300`
- Heavy Sharp-based watermarking in the main application
- Potential timeout and scaling issues

### After: Cloudflare Worker Integration
- Image generation offloaded to dedicated Cloudflare Worker
- Main application makes API calls to Worker
- Better performance, scalability, and cost efficiency
- Cleaner separation of concerns

## Changes Made

### 1. Cloudflare Worker (`/workers/image-gen/`)
- **`src/index.ts`**: Main Worker with API endpoints
- **`src/services/openai.ts`**: OpenAI integration (ported from main app)
- **`src/services/supabase.ts`**: Supabase client and database operations
- **`src/utils/watermark.ts`**: Watermarking utilities (Canvas API based)
- **`src/utils/auth.ts`**: API authentication
- **`src/utils/cors.ts`**: CORS handling

### 2. Main Application Updates
- **`lib/workers/image-api.ts`**: New Worker API client
- **`lib/bot/utils/openai-worker.ts`**: Wrapper for Worker API calls
- **`app/api/webhook/telegram/route.ts`**: Updated to use Worker API
- **`.env.local`**: Added Worker configuration variables

### 3. Deployment & Testing
- **`scripts/deploy-worker.ts`**: Automated Worker deployment
- **`scripts/test-worker.ts`**: Worker testing script
- **`package.json`**: Added Worker-related scripts

## Deployment Instructions

### 1. Deploy the Worker

```bash
# Navigate to worker directory
cd workers/image-gen

# Install dependencies
bun install

# Login to Cloudflare (if not already logged in)
bun wrangler login

# Set up environment variables
bun wrangler secret put OPENAI_API_KEY
bun wrangler secret put SUPABASE_URL
bun wrangler secret put SUPABASE_SERVICE_KEY
bun wrangler secret put WORKER_API_KEY

# Deploy the Worker
bun wrangler deploy

# Note the deployed URL
```

### 2. Update Main Application

```bash
# Update .env.local with your Worker URL
WORKER_BASE_URL=https://crybaby-image-worker.your-subdomain.workers.dev
WORKER_API_KEY=your-secure-worker-api-key

# Test the integration
bun run test-worker
```

### 3. Deploy Main Application

```bash
# Build and deploy as normal
bun run build
# Deploy to Vercel or your preferred platform
```

## Environment Variables

### Worker Secrets (set via `wrangler secret put`)
- `OPENAI_API_KEY`: Your OpenAI API key
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_KEY`: Your Supabase service role key
- `WORKER_API_KEY`: Secure key for main app → Worker authentication

### Main Application (`.env.local`)
- `WORKER_BASE_URL`: Your deployed Worker URL
- `WORKER_API_KEY`: Same key as Worker (for authentication)

## API Changes

### Function Signatures

**Before:**
```typescript
generateImage(prompt: string): Promise<ImageGenerationResult>
transformImage(imageUrl: string): Promise<ImageGenerationResult>
```

**After:**
```typescript
generateImage(prompt: string, userId: number): Promise<ImageGenerationResult>
transformImage(imageUrl: string, userId: number): Promise<ImageGenerationResult>
```

### Authentication

The Worker requires authentication via:
- `Authorization: Bearer <WORKER_API_KEY>` header, or
- `X-API-Key: <WORKER_API_KEY>` header

## Benefits Achieved

### Performance
- ✅ Faster response times through edge compute
- ✅ Reduced cold start issues
- ✅ Better resource utilization

### Scalability
- ✅ Automatic scaling without configuration
- ✅ Isolation from main application load
- ✅ Global edge distribution

### Cost Efficiency
- ✅ Pay-per-request model for image processing
- ✅ Reduced server costs for main application
- ✅ More efficient resource usage

### Reliability
- ✅ Cloudflare's global network reliability
- ✅ Better error handling and retries
- ✅ Improved fault tolerance

## Testing

### Local Testing
```bash
# Test Worker locally
cd workers/image-gen
bun run dev

# Test integration from main app
bun run test-worker
```

### Production Testing
```bash
# Health check
curl https://your-worker-url.workers.dev/health

# Test generation (requires authentication)
curl -X POST https://your-worker-url.workers.dev/generate \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "test image", "userId": 12345}'
```

## Monitoring

### Worker Logs
- View logs in Cloudflare dashboard
- Real-time logging with `wrangler tail`

### Main Application
- Existing Supabase logging continues to work
- Worker API calls are logged in main application

## Rollback Plan

If issues arise, you can quickly rollback:

1. Update `app/api/webhook/telegram/route.ts`:
   ```typescript
   // Change this line:
   import { generateImage, transformImage } from "@/lib/bot/utils/openai-worker";
   
   // Back to:
   import { generateImage, transformImage } from "@/lib/bot/utils/openai";
   ```

2. Redeploy the main application

## Future Improvements

### Short Term
- [ ] Enhanced watermarking using WebAssembly
- [ ] Image optimization and compression
- [ ] Better error handling and retries

### Long Term
- [ ] Caching layer for repeated requests
- [ ] Advanced analytics and metrics
- [ ] Multi-region deployment optimization
- [ ] Custom image processing pipelines

## Security Considerations

- ✅ API key authentication between main app and Worker
- ✅ CORS properly configured
- ✅ Environment variables securely managed
- ✅ Input validation and sanitization
- ✅ No sensitive data logged

## Troubleshooting

### Common Issues

1. **Worker not responding**: Check deployment and secrets
2. **Authentication failing**: Verify API key matches in both environments
3. **Timeouts**: Check Worker execution time and adjust timeouts
4. **CORS issues**: Verify CORS headers are properly set

### Debugging Commands

```bash
# View Worker logs
wrangler tail

# Test Worker health
curl https://your-worker-url.workers.dev/health

# Check main app configuration
bun run test-worker
```