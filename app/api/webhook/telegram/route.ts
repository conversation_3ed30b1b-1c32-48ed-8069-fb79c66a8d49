import { NextRequest, NextResponse } from "next/server";
import { Bo<PERSON>, webhookCallback } from "grammy";
import { checkGenerationAllowed, decrementBothLimits, getUserLimits, getGlobalSettings, logGeneration } from "@/lib/bot/utils/supabase";
import { generateImage, transformImage } from "@/lib/bot/utils/openai-worker";

// Increase timeout for image generation (up to 300 seconds for Pro plan)
export const maxDuration = 300;

console.log("🔧 Initializing Telegram webhook endpoint...");


// Create bot instance
const bot = new Bot(process.env.TELEGRAM_BOT_TOKEN!);

// Get bot info to know our own user ID
let BOT_USER_ID: number | undefined;
bot.api.getMe().then((botInfo) => {
  BOT_USER_ID = botInfo.id;
  console.log(`🤖 Bot initialized: @${botInfo.username} (ID: ${BOT_USER_ID})`);
}).catch(console.error);

console.log("🤖 Setting up bot commands...");

// Background processing function for text-to-image generation
async function processTextGeneration(
  userId: number,
  prompt: string,
  generationCheck: { user_remaining: number; global_remaining: number },
  chatId: number,
  processingMessageId: number
) {
  console.log(`🎨 Generating image for user ${userId} with prompt: "${prompt}"`);

  try {
    // Update progress: Starting generation
    try {
      await bot.api.editMessageText(
        chatId,
        processingMessageId,
        "🎨 **Creating your CryBaby masterpiece!**\n\n" +
        "🚀 **Starting DALL-E 3 generation...**\n" +
        "⏳ Generating image with AI...\n" +
        "🎭 Applying retro cartoon style...\n" +
        "✨ Adding watermark...\n\n" +
        "_This may take up to 2-3 minutes. Please be patient!_ 🕐",
        { parse_mode: "Markdown" }
      );
    } catch (editError) {
      console.warn(`⚠️ Could not update progress message: ${editError}`);
    }

    const result = await generateImage(prompt, userId);

    if (result.success && result.imageUrl) {
      console.log(`✅ Image generated successfully for user ${userId}: ${result.imageUrl}`);

      // Update progress: Generation complete, processing watermark
      try {
        await bot.api.editMessageText(
          chatId,
          processingMessageId,
          "🎨 **Creating your CryBaby masterpiece!**\n\n" +
          "✅ DALL-E 3 generation complete!\n" +
          "🎭 Retro cartoon style applied!\n" +
          "🔄 **Processing watermark...**\n" +
          "📤 Preparing final image...\n\n" +
          "_Almost done!_ ⏰",
          { parse_mode: "Markdown" }
        );
      } catch (editError) {
        console.warn(`⚠️ Could not update progress message: ${editError}`);
      }
      
      // Decrement both user and global limits atomically
      const decrementSuccess = await decrementBothLimits(userId);
      
      if (!decrementSuccess) {
        console.error(`❌ Failed to decrement limits for user ${userId}`);
        await bot.api.sendMessage(chatId, "❌ Limits changed while generating. Please try again.");
        return;
      }
      
      console.log(`✅ Limits decremented for user ${userId}`);
      
      // Log successful generation
      await logGeneration(userId, prompt, true, result.imageUrl);
      console.log(`📝 Generation logged for user ${userId}`);

      // Send the image
      await bot.api.sendPhoto(chatId, result.imageUrl, {
        caption: `🎨 Your CryBaby style image!\n\n` +
          `Prompt: "${prompt}"\n\n` +
          `Your remaining: ${generationCheck.user_remaining - 1} | ` +
          `Global remaining: ${generationCheck.global_remaining - 1}\n\n` +
          `✨ Watermarked with CryBaby logo`,
        parse_mode: "HTML"
      });

      console.log(`🖼️ Generated image sent to user ${userId}`);

      // Delete the "processing" message
      try {
        await bot.api.deleteMessage(chatId, processingMessageId);
      } catch (deleteError) {
        console.warn(`⚠️ Could not delete processing message: ${deleteError}`);
      }
    } else {
      console.error(`❌ Image generation failed for user ${userId}:`, result.error);
      
      // Log failed generation (don't decrement on failure)
      await logGeneration(userId, prompt, false, undefined, result.error);
      
      await bot.api.sendMessage(chatId, 
        `❌ Failed to generate image: ${result.error}\n\nPlease try again with a different prompt.`
      );
    }
  } catch (error) {
    console.error(`❌ Background generation error for user ${userId}:`, error);
    await bot.api.sendMessage(chatId, "❌ An error occurred while generating your image. Please try again.");
  }
}

// Background processing function for image transformation
async function processImageTransformation(
  userId: number,
  photoUrl: string,
  generationCheck: { user_remaining: number; global_remaining: number },
  chatId: number,
  processingMessageId: number
) {
  console.log(`🎨 Transforming image for user ${userId}...`);

  try {
    // Update progress: Starting transformation
    try {
      await bot.api.editMessageText(
        chatId,
        processingMessageId,
        "🎨 **Transforming your photo to CryBaby style!**\n\n" +
        "📥 Image downloaded successfully!\n" +
        "🚀 **Starting AI transformation...**\n" +
        "🔄 Creating artistic variation...\n" +
        "🎭 Applying retro cartoon effects...\n" +
        "✨ Adding watermark...\n\n" +
        "_This may take up to 2-3 minutes. Please be patient!_ 🕐",
        { parse_mode: "Markdown" }
      );
    } catch (editError) {
      console.warn(`⚠️ Could not update progress message: ${editError}`);
    }

    // Transform the uploaded image using OpenAI's variation model
    const result = await transformImage(photoUrl, userId);

    if (result.success && result.imageUrl) {
      console.log(`✅ Image transformed successfully for user ${userId}: ${result.imageUrl}`);

      // Update progress: Transformation complete
      try {
        await bot.api.editMessageText(
          chatId,
          processingMessageId,
          "🎨 **Transforming your photo to CryBaby style!**\n\n" +
          "📥 Image processed successfully!\n" +
          "✅ AI transformation complete!\n" +
          "🎭 Retro cartoon effects applied!\n" +
          "🔄 **Finalizing watermark...**\n" +
          "📤 Preparing your masterpiece...\n\n" +
          "_Almost ready!_ ⏰",
          { parse_mode: "Markdown" }
        );
      } catch (editError) {
        console.warn(`⚠️ Could not update progress message: ${editError}`);
      }
      
      // Decrement both user and global limits atomically
      const decrementSuccess = await decrementBothLimits(userId);
      
      if (!decrementSuccess) {
        console.error(`❌ Failed to decrement limits for user ${userId}`);
        await bot.api.sendMessage(chatId, "❌ Limits changed while processing. Please try again.");
        return;
      }
      
      console.log(`✅ Limits decremented for user ${userId}`);
      
      // Log successful generation
      await logGeneration(userId, "Direct image transformation", true, result.imageUrl);
      console.log(`📝 Generation logged for user ${userId}`);

      // Send the transformed image
      await bot.api.sendPhoto(chatId, result.imageUrl, {
        caption: `🎨 Transformed to CryBaby style!\n\n` +
          `Your remaining: ${generationCheck.user_remaining - 1} | ` +
          `Global remaining: ${generationCheck.global_remaining - 1}\n\n` +
          `✨ Watermarked with CryBaby logo\n\n` +
          `💡 Tip: Use /generate [description] for custom creations!`,
        parse_mode: "HTML"
      });

      console.log(`📸 Transformed image sent to user ${userId}`);

      // Delete the "processing" message
      try {
        await bot.api.deleteMessage(chatId, processingMessageId);
      } catch (deleteError) {
        console.warn(`⚠️ Could not delete processing message: ${deleteError}`);
      }
    } else {
      console.error(`❌ Image transformation failed for user ${userId}:`, result.error);
      
      // Log failed generation (don't decrement on failure)
      await logGeneration(userId, "Direct image transformation", false, undefined, result.error);
      
      await bot.api.sendMessage(chatId, 
        `❌ Failed to transform image: ${result.error}\n\nPlease try again or contact support if the issue persists.`
      );
    }
  } catch (error) {
    console.error(`❌ Background processing error for user ${userId}:`, error);
    await bot.api.sendMessage(chatId, "❌ An error occurred while processing your image. Please try again.");
  }
}

// Start command
bot.command("start", (ctx) => {
  console.log(`📱 Start command from user ${ctx.from?.id}`);
  ctx.reply(
    "🎨 Welcome to the CryBaby AI Image Bot!\n\n" +
    "<b>How to use:</b>\n" +
    "📸 <b>Send any photo</b> → Transform it to CryBaby style\n" +
    "✏️ <b>/generate [text]</b> → Create from description\n\n" +
    "<b>Commands:</b>\n" +
    "• /generate [prompt] - Generate an image from text\n" +
    "• /limit - Check remaining generations\n" +
    "• /help - Show this help message\n\n" +
    "Try sending me a photo or use:\n" +
    "/generate a cat wearing sunglasses",
    { parse_mode: "HTML" }
  );
});

// Help command
bot.command("help", (ctx) => {
  console.log(`❓ Help command from user ${ctx.from?.id}`);
  ctx.reply(
    "🤖 CryBaby AI Image Bot Help\n\n" +
    "<b>Two ways to create images:</b>\n\n" +
    "📸 <b>Direct Photo Transformation</b>\n" +
    "• Send any photo → Instant CryBaby style transformation\n" +
    "• Perfect for selfies, landscapes, objects\n\n" +
    "✏️ <b>Text-to-Image Generation</b>\n" +
    "• /generate [description] → Create from scratch\n" +
    "• Examples:\n" +
    "  - /generate a sunset over mountains\n" +
    "  - /generate a futuristic city\n" +
    "  - /generate a cute robot\n\n" +
    "<b>Other Commands:</b>\n" +
    "• /limit - Check remaining daily generations\n" +
    "• /help - Show this help message\n\n" +
    "<b>Features:</b>\n" +
    "• AI-powered by DALL-E 3\n" +
    "• Retro cartoon style with vintage palette\n" +
    "• Automatic CryBaby logo watermarking\n" +
    "• Daily limits: Personal & global capacity",
    { parse_mode: "HTML" }
  );
});

// Limit command
bot.command("limit", async (ctx) => {
  const userId = ctx.from?.id;
  console.log(`📊 Limit command from user ${userId}`);
  
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  try {
    const userLimits = await getUserLimits(userId);
    const globalSettings = await getGlobalSettings();
    
    if (!userLimits || !globalSettings) {
      console.error(`❌ Error fetching limits for user ${userId}`);
      await ctx.reply("❌ Error checking your limits. Please try again.");
      return;
    }

    console.log(`✅ Limits fetched for user ${userId}: user=${userLimits.remaining}, global=${globalSettings.remaining}`);

    await ctx.reply(
      `📊 Generation Limits Status\n\n` +
      `👤 Your Personal Limit:\n` +
      `• Daily limit: ${userLimits.daily_limit} images\n` +
      `• Remaining: ${userLimits.remaining} images\n\n` +
      `🌐 Global System Limit:\n` +
      `• Daily capacity: ${globalSettings.daily_limit} images\n` +
      `• Remaining: ${globalSettings.remaining} images\n\n` +
      `• Resets: Daily at midnight UTC\n` +
      `• Both limits must have capacity for generation`
    );
  } catch (error) {
    console.error(`❌ Error in limit command for user ${userId}:`, error);
    await ctx.reply("❌ Error checking your limits. Please try again.");
  }
});

// Generate command
bot.command("generate", async (ctx) => {
  const userId = ctx.from?.id;
  const prompt = ctx.message?.text?.split(" ").slice(1).join(" ");
  
  console.log(`🎨 Generate command from user ${userId} with prompt: "${prompt}"`);
  
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  if (!prompt) {
    await ctx.reply("❌ Please provide a prompt. Example: /generate a cat in space");
    return;
  }

  try {
    // Check both user and global limits
    console.log(`🔍 Checking generation limits for user ${userId}...`);
    const generationCheck = await checkGenerationAllowed(userId);
    if (!generationCheck) {
      console.error(`❌ Error checking limits for user ${userId}`);
      await ctx.reply("❌ Error checking your limits. Please try again.");
      return;
    }

    if (!generationCheck.allowed) {
      let message = "";
      
      if (generationCheck.reason === 'user_limit_exceeded') {
        console.log(`🚫 User limit exceeded for user ${userId}`);
        message = "🚫 Your daily limit exceeded!\n\n" +
          `You've used all ${generationCheck.user_limit} of your daily image generations.\n` +
          `Global system has ${generationCheck.global_remaining} images remaining.\n\n` +
          "Your limit resets daily at midnight UTC.";
      } else if (generationCheck.reason === 'global_limit_exceeded') {
        console.log(`🚫 Global limit exceeded (user ${userId})`);
        message = "🚫 System daily limit exceeded!\n\n" +
          `The global daily capacity of ${generationCheck.global_limit} images has been reached.\n` +
          `You have ${generationCheck.user_remaining} personal generations remaining.\n\n` +
          "The system resets daily at midnight UTC.";
      }
      
      await ctx.reply(message);
      return;
    }

    console.log(`✅ Generation allowed for user ${userId}. Starting image generation...`);

    // Send "generating" message and start background processing
    const generatingMessage = await ctx.reply(
      "🎨 **Creating your CryBaby masterpiece!**\n\n" +
      "⏳ Generating image with DALL-E 3...\n" +
      "🎭 Applying retro cartoon style...\n" +
      "✨ Adding watermark...\n\n" +
      "_This may take up to 2-3 minutes. Please be patient!_ 🕐",
      { parse_mode: "Markdown" }
    );

    // Enhanced prompt with specific art style
    const enhancedPrompt = `Retro cartoon illustration. Thick black outlines, smooth flat shading, limited warm vintage palette (muted oranges, ochres, teal accents). 1950s newspaper comic style, rounded shapes, subtle paper-grain texture. Clean vector aesthetic, high-resolution, simple background with soft abstract swirls in tan. ${prompt}`;
    
    console.log(`🎨 Generating image with enhanced prompt for user ${userId}...`);
    
    // Process text generation in background (don't await to avoid timeout)
    processTextGeneration(userId, enhancedPrompt, generationCheck, ctx.chat.id, generatingMessage.message_id)
      .catch(error => {
        console.error(`❌ Background generation failed for user ${userId}:`, error);
        // Send error message to user
        bot.api.sendMessage(ctx.chat.id, `❌ Failed to generate image: ${error.message}`);
      });
    
    // Webhook returns immediately to avoid timeout
    return;
  } catch (error) {
    console.error(`❌ Error in generate command for user ${userId}:`, error);
    await ctx.reply("❌ An unexpected error occurred. Please try again.");
  }
});

// Handle image messages (direct image transformation)
bot.on("message:photo", async (ctx) => {
  const userId = ctx.from?.id;
  const isBot = ctx.from?.is_bot;
  
  console.log(`📸 Photo received from user ${userId} (bot: ${isBot})`);
  
  // Skip if message is from a bot (including ourselves)
  if (isBot || userId === BOT_USER_ID) {
    console.log(`⏭️ Skipping photo from bot ${userId} (isBot: ${isBot}, our ID: ${BOT_USER_ID})`);
    return;
  }
  
  if (!userId) {
    await ctx.reply("❌ Unable to identify user");
    return;
  }

  try {
    // Check generation limits
    console.log(`🔍 Checking generation limits for user ${userId}...`);
    const generationCheck = await checkGenerationAllowed(userId);
    if (!generationCheck) {
      console.error(`❌ Error checking limits for user ${userId}`);
      await ctx.reply("❌ Error checking your limits. Please try again.");
      return;
    }

    if (!generationCheck.allowed) {
      let message = "";
      
      if (generationCheck.reason === 'user_limit_exceeded') {
        console.log(`🚫 User limit exceeded for user ${userId}`);
        message = "🚫 Your daily limit exceeded!\n\n" +
          `You've used all ${generationCheck.user_limit} of your daily image generations.\n` +
          `Global system has ${generationCheck.global_remaining} images remaining.\n\n` +
          "Your limit resets daily at midnight UTC.";
      } else if (generationCheck.reason === 'global_limit_exceeded') {
        console.log(`🚫 Global limit exceeded (user ${userId})`);
        message = "🚫 System daily limit exceeded!\n\n" +
          `The global daily capacity of ${generationCheck.global_limit} images has been reached.\n` +
          `You have ${generationCheck.user_remaining} personal generations remaining.\n\n` +
          "The system resets daily at midnight UTC.";
      }
      
      await ctx.reply(message);
      return;
    }

    console.log(`✅ Generation allowed for user ${userId}. Processing image...`);

    // Send "processing" message and start background processing
    const processingMessage = await ctx.reply(
      "🎨 **Transforming your photo to CryBaby style!**\n\n" +
      "📥 Processing your image...\n" +
      "🔄 Creating artistic variation...\n" +
      "🎭 Applying retro cartoon effects...\n" +
      "✨ Adding watermark...\n\n" +
      "_This may take up to 2-3 minutes. Please be patient!_ 🕐",
      { parse_mode: "Markdown" }
    );

    // Get the highest quality photo
    const photos = ctx.message.photo;
    const largestPhoto = photos[photos.length - 1]; // Get the largest version
    
    // Get file URL from Telegram
    const file = await ctx.api.getFile(largestPhoto.file_id);
    const photoUrl = `https://api.telegram.org/file/bot${process.env.TELEGRAM_BOT_TOKEN}/${file.file_path}`;
    
    console.log(`📷 Photo URL obtained: ${photoUrl}`);
    
    // Process image transformation in background (don't await to avoid timeout)
    processImageTransformation(userId, photoUrl, generationCheck, ctx.chat.id, processingMessage.message_id)
      .catch(error => {
        console.error(`❌ Background processing failed for user ${userId}:`, error);
        // Send error message to user
        bot.api.sendMessage(ctx.chat.id, `❌ Failed to transform image: ${error.message}`);
      });
    
    // Webhook returns immediately to avoid timeout
    return;
  } catch (error) {
    console.error(`❌ Error in photo handler for user ${userId}:`, error);
    
    // Log failed generation
    await logGeneration(userId, "Direct image transformation", false, undefined, "Unexpected error");

    await ctx.reply(
      "❌ An unexpected error occurred while transforming your image.\n" +
      "Please try again later or use /generate with a text description."
    );
  }
});

// Handle text messages without commands (provide helpful response)
bot.on("message:text", async (ctx) => {
  // Skip if it's a command
  if (ctx.message?.text?.startsWith("/")) return;
  
  const userId = ctx.from?.id;
  console.log(`💬 Text message from user ${userId}: "${ctx.message?.text}"`);
  
  await ctx.reply(
    "💡 <b>Tip:</b> To generate an image, use:\n\n" +
    "• <code>/generate [your description]</code> - Create from text\n" +
    "• Send a photo directly - Transform to CryBaby style\n\n" +
    "Example: <code>/generate a sunset over mountains</code>\n\n" +
    "Use /help for more information.",
    { parse_mode: "HTML" }
  );
});

// Error handler
bot.catch((err) => {
  console.error("🚨 Bot error:", err);
});

console.log("✅ Bot commands configured successfully");

// Create webhook callback for Next.js
const handleUpdate = webhookCallback(bot, "std/http");

export async function POST(request: NextRequest) {
  console.log("📨 Webhook POST request received");
  console.log("📋 Request headers:", Object.fromEntries(request.headers.entries()));
  
  try {
    const response = await handleUpdate(request);
    console.log("✅ Webhook processed successfully");
    return response;
  } catch (error) {
    console.error("❌ Webhook error:", error);
    return NextResponse.json({ error: "Webhook processing failed" }, { status: 500 });
  }
}

export async function GET() {
  console.log("🔍 Webhook GET request received (health check)");
  return NextResponse.json({ 
    status: "ok", 
    message: "Telegram webhook endpoint is running",
    timestamp: new Date().toISOString()
  });
}
