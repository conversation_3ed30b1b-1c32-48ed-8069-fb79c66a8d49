{"private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "setup-webhook": "bun run scripts/setup-webhook.ts", "test-webhook": "bun run scripts/test-webhook.ts", "verify-deployment": "bun run scripts/verify-deployment.ts", "register-commands": "bun run scripts/register-commands.ts", "deploy-worker": "bun run scripts/deploy-worker.ts", "test-worker": "bun run scripts/test-worker.ts"}, "dependencies": {"@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.12", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.0", "grammy": "^1.37.0", "lucide-react": "^0.511.0", "next": "latest", "next-themes": "^0.4.6", "openai": "^5.9.2", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "^9", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}